from collections import defaultdict
import logging
from uuid import UUI<PERSON>, uuid4
from django.db.models import Q
from django.db.models import <PERSON><PERSON><PERSON><PERSON>, TextField, BooleanField, SmallIntegerField
from django.shortcuts import get_object_or_404
from django.db import transaction
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.generics import RetrieveAPIView, CreateAPIView, ListAPIView, DestroyAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet
from rest_framework.views import APIView
from cls_backend.constants import BY_AI, BY_USER, DIEU_KHOAN_LIEN_QUAN, STATUS_FAILED, STATUS_PROCESSING, STATUS_PENDING, STATUS_SUCCESS, THAM_QUYEN_NOI_DUNG, VAN_BAN_CAN_CU, VAN_BAN_LIEN_QUAN_KHAC
from cls_backend.models import Document, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ument<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, WorkSpace
from cls_backend.modules.ocr.serializers import (
    DocumentRelatedSerializer, 
    DocumentSerializer, 
    LawClauseSerializer, 
    LawClauseLessInfoSerializer,
    DocumentRelatedLessInfoSerializer, 
    DocumentLessInfoSerializer, 
    RelatedLawClauseSerializer,
    TQNDSerializer,
)
from cls_backend.tasks.handle_ocr import handle_ocr
from cls_backend.tasks.term_review import term_review
from cls_backend import constants as const
from cls_backend.utils.es_utils import get_related_documents_from_es, init_es_client
from decouple import config
import requests
from django.utils.dateparse import parse_datetime
from cls_backend.utils.extract_clause_id import extract_clause_number
from cls_backend.utils.snake_case import to_snake_case
from cls_backend.utils.file_utils import is_valid_uuid
from request_log.decorators import log_requests


logger = logging.getLogger("cls")

class DocumentViewSet(ListAPIView, RetrieveAPIView, CreateAPIView, GenericViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = DocumentSerializer
    queryset = Document.objects.filter(deleted_at=None)


    def list(self, request):
        user = self.request.user
        search = request.query_params.get('search')
        sort_field = request.query_params.get('sort_field', '-created_at')
        workspace_id = request.query_params.get('workspace_id')
        # queryset = Document.objects.filter(deleted_at=None, workspace_id=workspace_id)
        if workspace_id:
            queryset = Document.objects.filter(deleted_at=None, workspace_id=workspace_id)
        else:
            default_workspace = WorkSpace.objects.filter(user=user, is_no_folder=True, is_deleted=False).first()
            queryset = Document.objects.filter(deleted_at=None,workspace_id = default_workspace.id)     
        if not user.is_superuser:
            queryset = queryset.filter(created_by=user)
        if search:
            queryset = queryset.filter(name__icontains=search)
        # if sort_field in ['name', 'ngay_ban_hanh', 'ngay_co_hieu_luc', 'id']:  # Đảm bảo chỉ sắp xếp theo các trường hợp lệ
        if sort_field != 'null':
            queryset = queryset.order_by(sort_field)  # Mặc định sẽ sắp xếp theo 'id'

        queryset = self.paginate_queryset(queryset)
        serializer = DocumentLessInfoSerializer(queryset, many=True)
        return self.get_paginated_response(serializer.data)

    def create(self, request, *args, **kwargs):
        # data = request.data.copy()
        data = request.data.dict()
        skip_process = False
        for field in ["ngay_dang_cong_bao", "ngay_het_hieu_luc", "ngay_het_hieu_luc_mot_phan", "ngay_ban_hanh", "ngay_co_hieu_luc"]:
            if data.get(field) == "":
                data[field] = None

        file = data.get('origin') or request.FILES.get('origin', None)
        
        if file:
            data['origin'] = file

        # If workspace_id is not provided, find or create a no-folder workspace
        if not data.get('workspace_id'):
            skip_process = True
            no_folder_workspace = WorkSpace.objects.filter(
                user=request.user,
                is_no_folder=True,
                is_deleted=False
            ).first()
            
            if not no_folder_workspace:
                no_folder_workspace = WorkSpace.objects.create(
                    user=request.user,
                    name="Không phân loại",
                    description="Workspace mặc định không phân loại",
                    is_no_folder=True
                )
            
            data['workspace_id'] = no_folder_workspace.id
        else:
            workspace = WorkSpace.objects.get(pk = data.get('workspace_id'))
            if workspace.is_no_folder:
                skip_process = True
        serializer = self.get_serializer(data=data)
        if serializer.is_valid():
            with transaction.atomic():
                name = file.name if file else data.get("title", "No name")
                if file:
                    document = serializer.save(
                        created_by=request.user,
                        name=name,
                        types=BY_USER,
                        status=const.STATUS_PENDING
                    )
                    handle_ocr.apply_async(kwargs={'document_id': document.id, 'skip_process': skip_process}, queue='ocr')
                else:
                    document = serializer.save(
                        created_by=request.user,
                        name=name,
                        types=BY_AI,
                        status=const.STATUS_PENDING
                    )
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


    @log_requests('add_search_document')
    @action(methods=['POST'], detail=False)
    def add_search_document(self, request):
        raw_data = request.data
        results, errors = [], []
        doc_mapping = {doc_data['id']: None for doc_data in raw_data}
        es_client = init_es_client()

        # Lấy dữ liệu toan_van từ ES
        resp = es_client.search(
            index=config('ELK_DOCUMENT_INDEX'),
            query={"terms": {"ID": list(doc_mapping.keys())}},
            _source=["ID", "toan_van"]
        )
        for hit in resp.get("hits", {}).get("hits", []):
            source = hit.get("_source", {})
            doc_mapping[source["ID"]] = source.get("toan_van", "")

        document_list = []
        document_id_map = {}  # Map từ es_id => Document instance
        es_ids = []

        for index, item in enumerate(raw_data):
            data = item.copy()
            data['es_id'] = data.get('id')
            for field in ["ngay_dang_cong_bao", "ngay_het_hieu_luc", "ngay_het_hieu_luc_mot_phan", "ngay_ban_hanh", "ngay_co_hieu_luc"]:
                if data.get(field) == "":
                    data[field] = None
            data['toan_van'] = doc_mapping.get(data['id'], '')

            serializer = self.get_serializer(data=data)
            if not serializer.is_valid():
                errors.append({"index": index, "errors": serializer.errors})
                continue

            try:
                with transaction.atomic():
                    document = serializer.save(
                        created_by=request.user,
                        name=data.get("title", f"No name {index}"),
                        types=BY_AI,
                        status=const.STATUS_SUCCESS
                    )
                    document_list.append(document)
                    document_id_map[document.es_id] = document
                    es_ids.append(document.es_id)

                    # Gọi lấy s3_url (tối ưu hóa sau)
                    try:
                        detail_resp = es_client.search(
                            index=config('ES_DETAIL_DOCUMENT'),
                            body={
                                "query": {"term": {"ID": document.es_id}},
                                "_source": ["s3_url"]
                            }
                        )
                        for hit in detail_resp.get("hits", {}).get("hits", []):
                            url_es = hit.get("_source", {}).get("s3_url")
                            if url_es:
                                doc_id = url_es.split("/")[-1].replace(".pdf", "")
                                document.origin = f"{doc_id}.pdf"
                                document.save(update_fields=["origin"])
                    except Exception as e:
                        logger.debug(f"Lỗi lấy s3_url: {e}")
            except Exception as e:
                errors.append({"index": index, "errors": str(e)})
                continue

            # results.append(serializer.data)

        # ✅ GỌI ES MỘT LẦN để lấy tất cả điều khoản liên quan
        try:
            clause_resp = es_client.search(
                index=config('ES_SEARCH_DIEU_KHOAN'),
                body={
                    "query": {
                        "terms": {
                            "id_document": es_ids
                        }
                    },
                    "_source": ["id", "title", "position", "content", "id_document"],
                    "size": 10000
                }
            )

            clauses_to_create = []
            for hit in clause_resp.get("hits", {}).get("hits", []):
                source = hit.get("_source", {})
                doc_id = source.get("id_document")
                related_doc = document_id_map.get(doc_id)
                if related_doc:
                    clauses_to_create.append(LawClause(
                        document=related_doc,
                        clause_id=source.get('id'),
                        title=source.get("title"),
                        position=source.get("position"),
                        content=source.get("content"),
                        doc_id=doc_id,
                        is_raw=True,
                        type=DIEU_KHOAN_LIEN_QUAN
                    ))

            if clauses_to_create:
                LawClause.objects.bulk_create(clauses_to_create, batch_size=1000)

        except Exception as e:
            logger.debug(f"Lỗi khi truy vấn ES để lấy điều khoản: {e}")
            errors.append({"error": f"Lỗi khi truy vấn điều khoản từ ES: {str(e)}"})

        if errors:
            return Response({"success": results, "errors": errors}, status=status.HTTP_207_MULTI_STATUS)
        
        return Response({"status": "success"}, status=status.HTTP_201_CREATED)

    
    @action(methods=['DELETE'], detail=True)
    def delete(self, request, pk):
        Document.objects.filter(id=pk).delete()
        return Response(status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=True)
    def rerun(self, request, pk):
        filters = {'id': pk, 'deleted_at': None}

        if not self.request.user.is_superuser:
            filters['created_by'] = self.request.user

        document = get_object_or_404(Document, **filters)
        document.status = STATUS_PENDING
        document.save(update_fields=['status'])
        workspace_id = document.workspace_id
        skip_process = WorkSpace.objects.get(pk = workspace_id).is_no_folder
        handle_ocr.apply_async(kwargs={'document_id': document.id, 'skip_process': True}, queue='ocr',skip_process=skip_process)

        return Response(status=status.HTTP_200_OK)
    
    @action(methods=['GET'], detail=True)
    def get_law_clauses(self, request, pk):
        clauses = LawClause.objects.filter(document=pk,deleted_at = None).order_by('id')
        serializer = LawClauseLessInfoSerializer(clauses, many=True)
        data = serializer.data

        # Sắp xếp lại theo số sau "dieu_"
        # sorted_data = sorted(data, key=lambda x: int(x.get("clause_id", "dieu_0").split("_")[1])) # case này bị lỗi khi clause_id=null
        # sorted_data = sorted(data, key=lambda x: extract_clause_number(x.get("clause_id")))

        # return Response(sorted_data, status=status.HTTP_200_OK)
        return Response(data, status=status.HTTP_200_OK)

    
    @action(methods=['GET'], detail=True)
    def get_law_clauses_search(self, request, pk):
        try:
            # logger.debug("vao day chu nhi")
            # Kiểm tra có phải UUID hợp lệ không
            uuid_obj = UUID(pk, version=4)
            # Lấy Document và es_id tương ứng
            document = DocumentRelated.objects.get(id=uuid_obj)
            document_ids = [document.ID]
        except (ValueError, Document.DoesNotExist):
            # Không phải UUID hợp lệ hoặc không tìm thấy Document → dùng pk như cũ
            document_ids = [pk]
        
        es = init_es_client()
        index = config('ES_SEARCH_DIEU_KHOAN')  # Không nối thêm _search gì cả!

        body = {
            "query": {
                "terms": {
                    "id_document": document_ids
                }
            },
            '_source': ['id', 'clause_id', 'title', 'position', 'doc_id', 'is_raw', 'type'],
            "size": 1000
        }

        try:
            if not es.indices.exists(index=index):
                return Response({"error": f"Index {index} not found in Elasticsearch."}, status=status.HTTP_400_BAD_REQUEST)

            es_response = es.search(index=index, body=body)
            hits = es_response.get("hits", {}).get("hits", [])
            
            law_clauses = []
            for item in hits:
                source = item.get("_source", {})
                law_clauses.append({
                    "id": str(uuid4()),
                    "clause_id": source.get('id'),
                    "title": source.get("title"),
                    "position": source.get("position"),
                    # "content": source.get("content"),
                    "doc_id": source.get("id_document"),
                    "is_raw": True,
                    "type": DIEU_KHOAN_LIEN_QUAN
                })
                
            # law_clauses.sort(key=lambda x: int(x.get("clause_id", "dieu_0").split("_")[1]))

            return Response(law_clauses, status=status.HTTP_200_OK)

        except Exception as e:
            logger.debug(f"Lỗi khi tìm kiếm trong Elasticsearch: {e}")
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


    @log_requests('view_law_clauses')
    @action(methods=['POST'], detail=True)
    def law_clauses(self, request, pk):
        data = request.data.copy()
        data['document_id'] = pk
        serializer = LawClauseSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            response = {
                'status': 'success',
                'message': 'Tạo điều khoản thành công'
            }
            return Response(response, status=status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
    @log_requests('search_law_clauses')
    @action(methods=['POST'], detail=True)
    def search_law_clauses(self, request, pk):
        ids = request.data['ids'].split(',')
        document = Document.objects.get(id=pk)
        force = request.data.get('force', False)
        # type_and_issuing_agency = extract_category(document.json_data)
        type_and_issuing_agency = {
            "loai_van_ban": document.loai_van_ban,  # giả sử đây là CharField
            "co_quan_ban_hanh_van_ban": [document.co_quan_ban_hanh]  # giả sử đây là CharField, nếu là M2M thì dùng .values_list
        }
        related_docs = DocumentRelated.objects.filter(document=document)

        payload = {
            "co_quan_ban_hanh": type_and_issuing_agency,
            "van_ban_can_cu": [],
            "van_ban_lien_quan": [],
        }
        for related_doc in related_docs:
            if related_doc.relation_type == VAN_BAN_CAN_CU:
                payload['van_ban_can_cu'].append(related_doc.ID)
            if related_doc.relation_type == VAN_BAN_LIEN_QUAN_KHAC:
                payload['van_ban_lien_quan'].append(related_doc.ID)
                
        for clause_id in ids:
            clause = LawClause.objects.get(id=clause_id)
            if clause.status != STATUS_SUCCESS:
                clause.status = STATUS_PENDING
                clause.save(update_fields=['status'])
                term_review.delay(payload, int(clause_id))
            elif force:  # Nếu có force=True và clause là STATUS_SUCCESS
                clause.status = STATUS_PENDING
                clause.save(update_fields=['status'])
                term_review.delay(payload, int(clause_id))
            
            
        return Response(status=status.HTTP_200_OK)

    @log_requests('view_luoc_do')
    @action(methods=['GET'], detail=True)
    def luoc_do(self, request, pk):
        def get_es_data(doc_id):
            es_client = init_es_client()
            if not es_client:
                return Response({"error": "Elasticsearch client initialization failed."}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            index_name = config('ES_DETAIL_DOCUMENT')
            related_data = get_related_documents_from_es(
                doc_id, es_client, index_name,
                include_fields=[
                    'ID',
                    'url',
                    'score',
                    'title',
                    'don_vi',
                    'so_hieu',
                    'nguoi_ky',
                    'trich_yeu',
                    'loai_van_ban',
                    'ngay_ban_hanh',
                    'co_quan_ban_hanh',
                    'ngay_co_hieu_luc',
                    'tinh_trang_hieu_luc',
                    'noi_dung',
                    'van_ban_lien_quan'
                ]
            )
            for item in related_data:
                if 'ID' in item:
                    item['id'] = item.pop('ID')
            return related_data
        
        if is_valid_uuid(pk):
            related_query = DocumentRelated.objects.filter(id=pk)
            if related_query.exists():
                related_doc = related_query.first()
                related_data = get_es_data(related_doc.ID)

                if related_data is None:
                    return Response({"error": "Document id not found."}, status=status.HTTP_404_NOT_FOUND)

                return Response(related_data, status=status.HTTP_200_OK)
            
            else:
                document = Document.objects.get(id=pk)
                filter_doc = request.data.get('filter', None)
                if document.types == BY_AI:
                    related_query = DocumentRelated.objects.filter(document=pk)
                    if not related_query.exists():
                        es_client = init_es_client()
                        ELK_DOCUMENT_INDEX = config('ELK_DOCUMENT_INDEX')
                        related_data = get_related_documents_from_es(document.es_id, es_client, ELK_DOCUMENT_INDEX)

                        if related_data is not None:
                            for relation_type, docs in related_data.items():
                                for doc_data in docs:
                                    DocumentRelated.objects.create(
                                        document=document,
                                        relation_type=relation_type,
                                        ID=doc_data.get('ID'),
                                        vbpl_id=doc_data.get('vbpl_id'),
                                        url=doc_data.get('url'),
                                        score=doc_data.get('score'),
                                        title=doc_data.get('title'),
                                        toan_van=doc_data.get('toan_van'),
                                        don_vi=doc_data.get('don_vi', []),
                                        so_hieu=doc_data.get('so_hieu'),
                                        nguoi_ky=doc_data.get('nguoi_ky'),
                                        trich_yeu=doc_data.get('trich_yeu'),
                                        loai_van_ban=doc_data.get('loai_van_ban'),
                                        ngay_ban_hanh=doc_data.get('ngay_ban_hanh'),
                                        co_quan_ban_hanh=doc_data.get('co_quan_ban_hanh'),
                                        ngay_co_hieu_luc=doc_data.get('ngay_co_hieu_luc'),
                                        tinh_trang_hieu_luc=doc_data.get('tinh_trang_hieu_luc'),
                                        noi_dung=doc_data.get('noi_dung')
                                    )

                kwargs = {
                    'document': pk
                }
                if filter_doc:
                    kwargs['ten_van_ban'] = filter_doc
                related_docs = DocumentRelated.objects.filter(**kwargs)
                serializer = DocumentRelatedLessInfoSerializer(related_docs, many=True)
                response = defaultdict(lambda: [])
                response[VAN_BAN_CAN_CU]
                response[VAN_BAN_LIEN_QUAN_KHAC]

                for doc_data in serializer.data:
                    response[doc_data['relation_type']].append(doc_data)

                return Response(response, status=status.HTTP_200_OK)
        else:
            related_data = get_es_data(pk)

            if related_data is None:
                return Response({"error": "Document id not found."}, status=status.HTTP_404_NOT_FOUND)

            return Response(related_data, status=status.HTTP_200_OK)

    
    
class LawClauseViewSet(ListAPIView, RetrieveAPIView, CreateAPIView, DestroyAPIView, GenericViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = LawClauseSerializer
    query_set = LawClause.objects.filter(deleted_at=None)

    def retrieve(self, request, pk):
        instance = LawClause.objects.get(pk=pk)
        serializer = LawClauseSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def update(self, request, pk):
        instance = LawClause.objects.get(pk=pk)
        serializer = LawClauseSerializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            response = {
                'status': 'success',
                'message': 'Cập nhật điều khoản thành công'
            }
            return Response(response, status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    
    def destroy(self, request, pk):
        query = LawClause.objects.filter(id=pk)

        if query.exists():
            query.delete()
            return Response(status=status.HTTP_200_OK)
        else:
            response = {
                'status': 'failed',
                'message': 'Điều khoản không tồn tại'
            }
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        
    @action(methods=['GET'], detail=True)
    def related_clauses(self, request, pk):        
        origin_clause = LawClause.objects.get(id=pk)
        related_clauses = origin_clause.related_clauses.filter(deleted_at=None)
        
        params = request.query_params
        filter_q = Q()
        for key, value in params.items():
            if hasattr(LawClause, key):
                field = LawClause._meta.get_field(key)

                if isinstance(field, BooleanField):
                    if value.lower() == 'true':
                        value = True
                    elif value.lower() == 'false':
                        value = False
                    filter_q &= Q(**{key: value})

                elif isinstance(field, (CharField, TextField)):
                    filter_q &= Q(**{f"{key}__icontains": value})

                elif isinstance(field, SmallIntegerField):
                    if ',' in value:
                        value_list = [v.strip() for v in value.split(',') if v.strip()]
                        filter_q &= Q(**{f"{key}__in": value_list})
                    else:
                        filter_q &= Q(**{key: value})

        related_clauses = related_clauses.filter(filter_q)
        related_clauses = self.paginate_queryset(related_clauses)
        
        serializer = LawClauseSerializer(related_clauses, many=True)
        
        doc_ids = {}
        for related_clause_data in serializer.data:
            doc_ids[related_clause_data['doc_id']] = {}

        # logger.debug(doc_ids)
        es_client = init_es_client()
        ELK_DOCUMENT_INDEX = config('ELK_DOCUMENT_INDEX')
        query = {"terms": {'ID': list(doc_ids.keys())}}
        resp = es_client.search(index=ELK_DOCUMENT_INDEX, query=query, size=len(doc_ids))
        # logger.debug(resp)
        if resp["hits"]["total"]["value"]>0:
            for document_data in resp["hits"]["hits"]:
                document_data = document_data['_source']
                doc_ids[str(document_data['ID'])] = {
                    'title': document_data.get('title'),
                    'ngay_ban_hanh': document_data.get('ngay_ban_hanh'),
                    'ngay_co_hieu_luc': document_data.get('ngay_co_hieu_luc'),
                    'tinh_trang_hieu_luc': document_data.get('tinh_trang_hieu_luc'),
                }

        # logger.debug(doc_ids)
        for related_clause_data in serializer.data:
            related_clause_data['document_data'] = doc_ids[related_clause_data['doc_id']]

        return self.get_paginated_response(serializer.data)
    
    @action(methods=['GET'], detail=True)
    def tham_quyen_noi_dung(self, request, pk):        
        origin_clause = LawClause.objects.get(id=pk)
        doc_ids = {}

        tham_quyen_noi_dungs = ThamQuyenNoiDung.objects.filter(lawclause=origin_clause)
        tqnd_serializer = TQNDSerializer(tham_quyen_noi_dungs, many=True)
        
        tqnd_related_clauses = LawClause.objects.filter(tqnd_related_clauses__in=tham_quyen_noi_dungs)
        tqnd_related_clauses_serializer = LawClauseSerializer(tqnd_related_clauses, many=True)
        tqnd_related_clauses_mapping = {}
        for related_clause_data in tqnd_related_clauses_serializer.data:
            tqnd_related_clauses_mapping[related_clause_data['id']] = related_clause_data

        for tqnd_data in tqnd_serializer.data:
            for related_clause_id in tqnd_data['related_clauses']:
                doc_ids[related_clause_id] = {}
            tqnd_data['quy_dinh_phap_luat_lien_quan'] = [
                tqnd_related_clauses_mapping[related_clause_id]
                for related_clause_id in tqnd_data['related_clauses']
            ]

        return Response(tqnd_serializer.data, status=status.HTTP_200_OK)

    @action(methods=['POST'], detail=True)
    def add_related_clauses(self, request, pk):    
        es_client = init_es_client()
        legal_ids = request.data.get('clause_id').split(',')
        ELK_TERM_INDEX = config('ES_SEARCH_DIEU_KHOAN')
        query = {"terms": {'_id': legal_ids}}
        resp = es_client.search(index=ELK_TERM_INDEX, query=query)
        # logger.debug(resp)
        for legal_data in resp["hits"]["hits"]:
            legal_data = legal_data['_source']
            related_clause = LawClause.objects.create(
                clause_id=legal_data.get('id'),
                title=legal_data.get("title"),
                position=legal_data.get("position"),
                content=legal_data.get("content"),
                doc_id=legal_data.get("id_document"),
                is_raw=True,
                type=DIEU_KHOAN_LIEN_QUAN
            )
            related_clause.related_clauses.add(pk)
        response = {
            'status': 'success',
            'message': 'Thêm điều khoản thành công'
        }
        return Response(response, status.HTTP_200_OK)
    
    
    @action(methods=['PUT'], detail=True)
    def update_different_status(self, request, pk):
        req_status_str = request.data.get('different_status')
        if req_status_str:
            dif_status = True if req_status_str.lower() == "true" else False
            clause = LawClause.objects.get(id=pk)
            clause.different_status = dif_status
            clause.save()
    
            return Response({
                "Updated different_status": dif_status
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                "Missing different_status"
            }, status=status.HTTP_400_BAD_REQUEST)
    
    @action(methods=['POST'], detail=True)
    def save_compare_result(self, request, pk):
        ly_do = request.data.get("ly_do", None)
        ket_luan = request.data.get("ket_luan", None)
        giai_phap = request.data.get("giai_phap", None)
        
        related_clause = LawClause.objects.get(pk=pk)
        
        if related_clause.document:
            return Response({
                "Should be id of related law clause, not clause in document"
            }, status=status.HTTP_400_BAD_REQUEST) 
        
        related_clause.ly_do = ly_do
        related_clause.ket_luan = ket_luan
        related_clause.reason = ket_luan
        related_clause.giai_phap = giai_phap
        related_clause.save()
        
        return Response({
            "Updated ly_do": ly_do,
            "Updated ket_luan": ket_luan,
            "Updated giai_phap": giai_phap
        }, status=status.HTTP_200_OK)
        

            
class DocumentRelatedView(ListAPIView, RetrieveAPIView, CreateAPIView, DestroyAPIView, GenericViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = DocumentRelatedSerializer
    query_set = DocumentRelated.objects.all()

    def create(self, request):
        """Thêm danh sách văn bản liên quan từ ES vào document pk"""
        # pk = kwargs.get('pk')
        pk = request.data.get('id')
        document = Document.objects.get(pk = pk)
        es_client = init_es_client()
        related_ids = request.data.get('document_ids')  # danh sách id từ ES
        relation_type = request.data.get('relation_type')  # ví dụ: "van_ban_can_cu"

        if not related_ids:
            return Response({"error": "Thiếu document_ids"}, status=status.HTTP_400_BAD_REQUEST)
        if not relation_type:
            return Response({"error": "Thiếu relation_type"}, status=status.HTTP_400_BAD_REQUEST)

        ELK_VANBAN_INDEX = config('ES_DETAIL_DOCUMENT')  # index ES chứa dữ liệu văn bản
        query = {"terms": {'ID': related_ids}}
        resp = es_client.search(index=ELK_VANBAN_INDEX, query=query)

        created_docs = []
        for doc in resp["hits"]["hits"]:
            doc_data = doc["_source"]

            toan_van = doc_data.get('toan_van')
            if not toan_van:
                toan_van = doc_data.get('cleaned_toan_van')

            related_doc = DocumentRelated.objects.create(
                document=document,
                relation_type=relation_type,
                ID=doc_data.get('ID'),
                score=doc.get('_score'),
                url=doc_data.get('url'),
                title=doc_data.get('title'),
                toan_van=toan_van,
                so_hieu=doc_data.get('so_hieu'),
                nguoi_ky=doc_data.get('nguoi_ky'),
                don_vi=doc_data.get('don_vi') or [],
                trich_yeu=doc_data.get('trich_yeu'),
                loai_van_ban=doc_data.get('loai_van_ban'),
                ngay_ban_hanh=parse_datetime(doc_data.get('ngay_ban_hanh')) if doc_data.get('ngay_ban_hanh') else None,
                ngay_co_hieu_luc=parse_datetime(doc_data.get('ngay_co_hieu_luc')) if doc_data.get('ngay_co_hieu_luc') else None,
                co_quan_ban_hanh=doc_data.get('co_quan_ban_hanh'),
                tinh_trang_hieu_luc=doc_data.get('tinh_trang_hieu_luc'),
            )

            created_docs.append(related_doc.id)

        return Response({
            "status": "success",
            "message": f"Đã thêm {len(created_docs)} văn bản căn cứ",
            "related_ids": created_docs
        }, status=status.HTTP_201_CREATED)

    def destroy(self, request, pk):
        """Xoá văn bản liên quan theo ID"""
        # pk = kwargs.get('pk')
        # pk = request.data.get('id')
        # print('##################', pk)
        if not pk:
            return Response({"error": "Thiếu ID để xóa"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            document_related = DocumentRelated.objects.get(pk=pk)
            document_related.delete()
            return Response({"message": "Xoá thành công"}, status=status.HTTP_204_NO_CONTENT)
        except DocumentRelated.DoesNotExist:
            return Response({"error": "Không tìm thấy văn bản liên quan"}, status=status.HTTP_404_NOT_FOUND)
            
